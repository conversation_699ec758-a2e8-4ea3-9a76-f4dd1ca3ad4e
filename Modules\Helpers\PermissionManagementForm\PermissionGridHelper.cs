using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using DevExpress.XtraGrid;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraEditors.Repository;
using DevExpress.XtraEditors.Controls;
using ProManage.Modules.Models;
using ProManage.Modules.Services;

namespace ProManage.Modules.Helpers.PermissionManagementForm
{
    /// <summary>
    /// Helper class for managing permission grids in permission management forms.
    /// Provides grid configuration, data binding, and event handling for permission editing.
    /// </summary>
    public class PermissionGridHelper
    {
        #region Grid Configuration

        /// <summary>
        /// Configure role permissions grid with checkbox editing
        /// </summary>
        /// <param name="gridControl">Grid control to configure</param>
        /// <param name="gridView">Grid view to configure</param>
        public void ConfigureRolePermissionsGrid(GridControl gridControl, GridView gridView)
        {
            try
            {
                // Clear existing columns and repository items
                gridView.Columns.Clear();
                gridControl.RepositoryItems.Clear();

                // Create checkbox repository item
                var checkBoxRepository = new RepositoryItemCheckEdit();
                checkBoxRepository.Name = "CheckBoxRepository";
                checkBoxRepository.ValueChecked = true;
                checkBoxRepository.ValueUnchecked = false;
                checkBoxRepository.NullStyle = DevExpress.XtraEditors.Controls.StyleIndeterminate.Unchecked;
                gridControl.RepositoryItems.Add(checkBoxRepository);

                // Configure grid view options
                gridView.OptionsView.ShowGroupPanel = false;
                gridView.OptionsView.ShowAutoFilterRow = true;
                gridView.OptionsBehavior.Editable = true;
                gridView.OptionsSelection.EnableAppearanceFocusedCell = false;
                gridView.OptionsSelection.EnableAppearanceFocusedRow = true;
                gridView.OptionsView.ColumnAutoWidth = false;

                // Add columns
                AddColumn(gridView, "FormName", "Form Name", 200, false);
                AddColumn(gridView, "DisplayName", "Display Name", 250, false);
                AddColumn(gridView, "Category", "Category", 120, false);
                AddCheckboxColumn(gridView, "ReadPermission", "Read", 80, checkBoxRepository);
                AddCheckboxColumn(gridView, "NewPermission", "New", 80, checkBoxRepository);
                AddCheckboxColumn(gridView, "EditPermission", "Edit", 80, checkBoxRepository);
                AddCheckboxColumn(gridView, "DeletePermission", "Delete", 80, checkBoxRepository);
                AddCheckboxColumn(gridView, "PrintPermission", "Print", 80, checkBoxRepository);

                // Configure appearance
                ConfigureGridAppearance(gridView);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error configuring role permissions grid: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Configure user permissions grid with role vs override color coding
        /// </summary>
        /// <param name="gridControl">Grid control to configure</param>
        /// <param name="gridView">Grid view to configure</param>
        public void ConfigureUserPermissionsGrid(GridControl gridControl, GridView gridView)
        {
            try
            {
                // Clear existing columns and repository items
                gridView.Columns.Clear();
                gridControl.RepositoryItems.Clear();

                // Create checkbox repository item
                var checkBoxRepository = new RepositoryItemCheckEdit();
                checkBoxRepository.Name = "CheckBoxRepository";
                checkBoxRepository.ValueChecked = true;
                checkBoxRepository.ValueUnchecked = false;
                checkBoxRepository.NullStyle = DevExpress.XtraEditors.Controls.StyleIndeterminate.Unchecked;
                gridControl.RepositoryItems.Add(checkBoxRepository);

                // Configure grid view options
                gridView.OptionsView.ShowGroupPanel = false;
                gridView.OptionsView.ShowAutoFilterRow = true;
                gridView.OptionsBehavior.Editable = true;
                gridView.OptionsSelection.EnableAppearanceFocusedCell = false;
                gridView.OptionsSelection.EnableAppearanceFocusedRow = true;
                gridView.OptionsView.ColumnAutoWidth = false;

                // Add columns
                AddColumn(gridView, "FormName", "Form Name", 180, false);
                AddColumn(gridView, "DisplayName", "Display Name", 200, false);
                AddColumn(gridView, "Category", "Category", 100, false);

                // Role permissions (read-only, for reference) - inactive as requested
                AddColumn(gridView, "RoleRead", "Role Read", 80, false);

                // User permissions (editable) - simplified as requested
                AddCheckboxColumn(gridView, "ReadPermission", "Read", 80, checkBoxRepository);
                AddCheckboxColumn(gridView, "WritePermission", "Write", 80, checkBoxRepository);
                AddCheckboxColumn(gridView, "DeletePermission", "Delete", 80, checkBoxRepository);
                AddCheckboxColumn(gridView, "PrintPermission", "Print", 80, checkBoxRepository);

                // Configure appearance with color coding
                ConfigureUserPermissionsAppearance(gridView);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error configuring user permissions grid: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region Data Binding

        /// <summary>
        /// Load role permissions data into grid
        /// </summary>
        /// <param name="gridControl">Grid control to load data into</param>
        /// <param name="roleId">Role ID to load permissions for</param>
        public void LoadRolePermissions(GridControl gridControl, int roleId)
        {
            try
            {
                var permissions = GetRolePermissionsForGrid(roleId);
                gridControl.DataSource = permissions;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading role permissions: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Load user permissions data into grid with role comparison
        /// </summary>
        /// <param name="gridControl">Grid control to load data into</param>
        /// <param name="userId">User ID to load permissions for</param>
        public void LoadUserPermissions(GridControl gridControl, int userId)
        {
            try
            {
                var permissions = GetUserPermissionsForGrid(userId);
                gridControl.DataSource = permissions;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading user permissions: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// Add a regular column to the grid view
        /// </summary>
        private void AddColumn(GridView gridView, string fieldName, string caption, int width, bool allowEdit)
        {
            var column = gridView.Columns.AddField(fieldName);
            column.Caption = caption;
            column.Width = width;
            column.OptionsColumn.AllowEdit = allowEdit;
            column.OptionsColumn.AllowFocus = allowEdit;
            column.Visible = true;
        }

        /// <summary>
        /// Add a checkbox column to the grid view
        /// </summary>
        private void AddCheckboxColumn(GridView gridView, string fieldName, string caption, int width, 
            RepositoryItemCheckEdit checkBoxRepository)
        {
            var column = gridView.Columns.AddField(fieldName);
            column.Caption = caption;
            column.Width = width;
            column.ColumnEdit = checkBoxRepository;
            column.OptionsColumn.AllowEdit = true;
            column.OptionsColumn.AllowFocus = true;
            column.Visible = true;
        }

        /// <summary>
        /// Configure basic grid appearance
        /// </summary>
        private void ConfigureGridAppearance(GridView gridView)
        {
            // Set alternating row colors
            gridView.OptionsView.EnableAppearanceEvenRow = true;
            gridView.Appearance.EvenRow.BackColor = Color.FromArgb(245, 245, 245);
            
            // Set header appearance
            gridView.Appearance.HeaderPanel.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            gridView.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            
            // Set row height
            gridView.RowHeight = 25;
        }

        /// <summary>
        /// Configure user permissions grid appearance with color coding
        /// </summary>
        private void ConfigureUserPermissionsAppearance(GridView gridView)
        {
            ConfigureGridAppearance(gridView);
            
            // Add custom row cell style event for color coding
            gridView.RowCellStyle += (sender, e) =>
            {
                if (e.Column.FieldName.StartsWith("Role"))
                {
                    e.Appearance.BackColor = Color.FromArgb(230, 230, 250); // Light blue for role permissions
                }
                else if (e.Column.FieldName.StartsWith("User"))
                {
                    e.Appearance.BackColor = Color.FromArgb(255, 250, 230); // Light yellow for user overrides
                }
                else if (e.Column.FieldName.StartsWith("Effective"))
                {
                    e.Appearance.BackColor = Color.FromArgb(230, 255, 230); // Light green for effective permissions
                    e.Appearance.Font = new Font(e.Appearance.Font, FontStyle.Bold);
                }
            };
        }

        /// <summary>
        /// Get role permissions formatted for grid display
        /// </summary>
        private DataTable GetRolePermissionsForGrid(int roleId)
        {
            var dataTable = new DataTable();
            dataTable.Columns.Add("FormName", typeof(string));
            dataTable.Columns.Add("DisplayName", typeof(string));
            dataTable.Columns.Add("Category", typeof(string));
            dataTable.Columns.Add("ReadPermission", typeof(bool));
            dataTable.Columns.Add("NewPermission", typeof(bool));
            dataTable.Columns.Add("EditPermission", typeof(bool));
            dataTable.Columns.Add("DeletePermission", typeof(bool));
            dataTable.Columns.Add("PrintPermission", typeof(bool));

            // Get all forms and their permissions for this role
            var forms = FormsConfigurationService.GetAllForms();
            var rolePermissions = PermissionService.GetRolePermissions(roleId);

            foreach (var form in forms)
            {
                var permission = rolePermissions.FirstOrDefault(p => p.FormName == form.FormName);
                var row = dataTable.NewRow();
                row["FormName"] = form.FormName;
                row["DisplayName"] = form.DisplayName;
                row["Category"] = form.Category;
                row["ReadPermission"] = permission?.ReadPermission ?? false;
                row["NewPermission"] = permission?.NewPermission ?? false;
                row["EditPermission"] = permission?.EditPermission ?? false;
                row["DeletePermission"] = permission?.DeletePermission ?? false;
                row["PrintPermission"] = permission?.PrintPermission ?? false;
                dataTable.Rows.Add(row);
            }

            return dataTable;
        }

        /// <summary>
        /// Get user permissions formatted for grid display with simplified structure
        /// </summary>
        private DataTable GetUserPermissionsForGrid(int userId)
        {
            var dataTable = new DataTable();
            // Form info columns
            dataTable.Columns.Add("FormName", typeof(string));
            dataTable.Columns.Add("DisplayName", typeof(string));
            dataTable.Columns.Add("Category", typeof(string));

            // Role permissions (read-only reference) - only Read as requested
            dataTable.Columns.Add("RoleRead", typeof(bool));

            // User permissions (editable) - simplified structure
            dataTable.Columns.Add("ReadPermission", typeof(bool));
            dataTable.Columns.Add("WritePermission", typeof(bool)); // This represents both New and Edit
            dataTable.Columns.Add("DeletePermission", typeof(bool));
            dataTable.Columns.Add("PrintPermission", typeof(bool));

            // Get user's effective permissions
            var userPermissions = PermissionService.GetUserEffectivePermissions(userId);
            var forms = FormsConfigurationService.GetAllForms();

            foreach (var form in forms)
            {
                var permission = userPermissions.FirstOrDefault(p => p.FormName == form.FormName);
                var row = dataTable.NewRow();
                row["FormName"] = form.FormName;
                row["DisplayName"] = form.DisplayName;
                row["Category"] = form.Category;
                
                if (permission != null)
                {
                    // Role permissions (only Read as requested)
                    row["RoleRead"] = permission.RoleReadPermission;

                    // User permissions (effective permissions - user overrides take precedence)
                    row["ReadPermission"] = permission.ReadPermission;
                    row["WritePermission"] = permission.EditPermission; // Write represents Edit capability
                    row["DeletePermission"] = permission.DeletePermission;
                    row["PrintPermission"] = permission.PrintPermission;
                }
                else
                {
                    // No permissions found - set all to false
                    row["RoleRead"] = false;
                    row["ReadPermission"] = false;
                    row["WritePermission"] = false;
                    row["DeletePermission"] = false;
                    row["PrintPermission"] = false;
                }
                
                dataTable.Rows.Add(row);
            }

            return dataTable;
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// Add a user permission column with ComboBox editor for Inherit/True/False values
        /// </summary>
        /// <param name="gridControl">Grid control to add repository item to</param>
        /// <param name="gridView">Grid view to add column to</param>
        /// <param name="fieldName">Field name for the column</param>
        /// <param name="caption">Column caption</param>
        /// <param name="width">Column width</param>
        private void AddUserPermissionColumn(GridControl gridControl, GridView gridView, string fieldName, string caption, int width)
        {
            // Create ComboBox repository item
            var comboBoxRepository = new RepositoryItemComboBox();
            comboBoxRepository.Items.AddRange(new string[] { "Inherit", "True", "False" });
            comboBoxRepository.TextEditStyle = TextEditStyles.DisableTextEditor;
            gridControl.RepositoryItems.Add(comboBoxRepository);

            // Add column
            var column = gridView.Columns.AddField(fieldName);
            column.Caption = caption;
            column.Width = width;
            column.Visible = true;
            column.ColumnEdit = comboBoxRepository;
            column.OptionsColumn.AllowEdit = true;
        }

        /// <summary>
        /// Convert nullable boolean to string representation for grid display
        /// </summary>
        /// <param name="value">Nullable boolean value</param>
        /// <returns>String representation: "True", "False", or "Inherit"</returns>
        private string ConvertNullableBoolToString(bool? value)
        {
            if (value == null)
                return "Inherit";
            return value.Value ? "True" : "False";
        }

        /// <summary>
        /// Convert string representation back to nullable boolean
        /// </summary>
        /// <param name="value">String value from grid</param>
        /// <returns>Nullable boolean value</returns>
        private bool? ConvertStringToNullableBool(string value)
        {
            if (string.IsNullOrEmpty(value) || value == "Inherit")
                return null;
            if (value == "True")
                return true;
            if (value == "False")
                return false;
            return null;
        }

        #endregion
    }
}
